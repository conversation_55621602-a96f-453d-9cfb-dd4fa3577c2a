'use client';

import { useState, useEffect, Suspense } from 'react';
import dynamic from 'next/dynamic';
import { useParams, useSearchParams } from 'next/navigation';
import { Locale } from '../../../lib/i18n';
import { getTranslation, TranslationKey, NestedTranslationKey } from '../../../lib/translations';
import Navbar from '../../../components/Navbar';
import Footer from '../../../components/Footer';
import ProductCard from '../../../components/ProductCard';
import { ProductWithDetails, Category, Subcategory } from '../../../types/mysql-database';

// تحميل ديناميكي لزر الواتساب
const WhatsAppButton = dynamic(() => import('../../../components/WhatsAppButton'), {
  loading: () => null
});

// Component that uses useSearchParams
function ProductsContent({ locale }: { locale: Locale }) {
  const searchParams = useSearchParams();

  const [products, setProducts] = useState<ProductWithDetails[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [currentCategory, setCurrentCategory] = useState<Category | null>(null);
  const [currentSubcategory, setCurrentSubcategory] = useState<Subcategory | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  // الحصول على معاملات URL
  const categoryId = searchParams?.get('category');
  const subcategoryId = searchParams?.get('subcategory');

  const t = (key: TranslationKey | NestedTranslationKey) => getTranslation(locale, key);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // تحديد URL للمنتجات حسب الفلترة
        let productsUrl = '/api/products';
        if (subcategoryId) {
          productsUrl += `?subcategoryId=${subcategoryId}`;
        } else if (categoryId) {
          productsUrl += `?categoryId=${categoryId}`;
        }

        // جلب المنتجات
        const productsResponse = await fetch(productsUrl);
        if (productsResponse.ok) {
          const productsResult = await productsResponse.json();
          console.log('📦 استجابة API المنتجات:', productsResult);

          if (productsResult.success && productsResult.data) {
            setProducts(productsResult.data);
          } else {
            console.error('❌ فشل في جلب المنتجات:', productsResult);
            setProducts([]);
          }
        } else {
          console.error('❌ خطأ في استجابة المنتجات:', productsResponse.status);
          setProducts([]);
        }

        // جلب الفئات
        const categoriesResponse = await fetch('/api/categories');
        if (categoriesResponse.ok) {
          const categoriesResult = await categoriesResponse.json();
          console.log('📦 استجابة API الفئات:', categoriesResult);

          if (categoriesResult.success && categoriesResult.data) {
            setCategories(categoriesResult.data);
          } else {
            console.error('❌ فشل في جلب الفئات:', categoriesResult);
            setCategories([]);
          }
        }

        // جلب معلومات الفئة الحالية إذا كانت محددة
        if (categoryId) {
          const categoryResponse = await fetch(`/api/categories/${categoryId}`);
          if (categoryResponse.ok) {
            const categoryResult = await categoryResponse.json();
            if (categoryResult.success && categoryResult.data) {
              setCurrentCategory(categoryResult.data);
            }
          }
        }

        // جلب معلومات الفئة الفرعية إذا كانت محددة
        if (subcategoryId) {
          const subcategoryResponse = await fetch(`/api/subcategories/${subcategoryId}`);
          if (subcategoryResponse.ok) {
            const subcategoryResult = await subcategoryResponse.json();
            if (subcategoryResult.success && subcategoryResult.data) {
              setCurrentSubcategory(subcategoryResult.data);

              // جلب الفئة الرئيسية أيضاً
              if (subcategoryResult.data.category_id) {
                const parentCategoryResponse = await fetch(`/api/categories/${subcategoryResult.data.category_id}`);
                if (parentCategoryResponse.ok) {
                  const parentCategoryResult = await parentCategoryResponse.json();
                  if (parentCategoryResult.success && parentCategoryResult.data) {
                    setCurrentCategory(parentCategoryResult.data);
                  }
                }
              }
            }
          }
        }

      } catch (error) {
        console.error('Error fetching data:', error);
        setProducts([]);
        setCategories([]);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [categoryId, subcategoryId]);

  const filteredProducts = products.filter(product => {
    const matchesCategory = selectedCategory === 'all' || product.category_id === selectedCategory;
    const searchField = locale === 'ar' ? product.title_ar : product.title;
    const matchesSearch = searchField.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch && product.is_active;
  });

  return (
    <>
      <main>
        {/* Page Header */}
        <section className="bg-primary py-12">
          <div className="container mx-auto px-4">
            {/* Breadcrumb */}
            {(currentCategory || currentSubcategory) && (
              <nav className="mb-6 text-center">
                <ol className="inline-flex items-center space-x-2 rtl:space-x-reverse text-sm text-white/80">
                  <li>
                    <a href={`/${locale}`} className="hover:text-white transition-colors">
                      {t('home')}
                    </a>
                  </li>
                  <i className="ri-arrow-right-s-line mx-2"></i>
                  <li>
                    <a href={`/${locale}/products`} className="hover:text-white transition-colors">
                      {t('products')}
                    </a>
                  </li>
                  {currentCategory && (
                    <>
                      <i className="ri-arrow-right-s-line mx-2"></i>
                      <li>
                        <a
                          href={`/${locale}/products?category=${currentCategory.id}`}
                          className={`transition-colors ${currentSubcategory ? 'hover:text-white' : 'text-white font-medium'}`}
                        >
                          {locale === 'ar' ? currentCategory.name_ar : currentCategory.name}
                        </a>
                      </li>
                    </>
                  )}
                  {currentSubcategory && (
                    <>
                      <i className="ri-arrow-right-s-line mx-2"></i>
                      <li className="text-white font-medium">
                        {locale === 'ar' ? currentSubcategory.name_ar : currentSubcategory.name}
                      </li>
                    </>
                  )}
                </ol>
              </nav>
            )}

            <h1 className="text-3xl md:text-4xl font-bold text-white text-center">
              {currentSubcategory
                ? (locale === 'ar' ? currentSubcategory.name_ar : currentSubcategory.name)
                : currentCategory
                ? (locale === 'ar' ? currentCategory.name_ar : currentCategory.name)
                : t('products')
              }
            </h1>
            <p className="text-white/80 text-center mt-4 max-w-2xl mx-auto">
              {currentSubcategory && locale === 'ar' ? currentSubcategory.description_ar :
               currentSubcategory && locale === 'en' ? currentSubcategory.description :
               currentCategory && locale === 'ar' ? currentCategory.description_ar :
               currentCategory && locale === 'en' ? currentCategory.description :
               locale === 'ar'
                ? 'اكتشف مجموعتنا الواسعة من معدات المطاعم والفنادق عالية الجودة'
                : 'Discover our wide range of high-quality restaurant and hotel equipment'
              }
            </p>
            {products.length > 0 && (
              <div className="text-center mt-4 text-white/60 text-sm">
                {products.length} {locale === 'ar' ? 'منتج متاح' : 'products available'}
              </div>
            )}
          </div>
        </section>

        {/* Products Section */}
        <section className="py-12">
          <div className="container mx-auto px-4">
            {/* Filters */}
            <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 mb-8">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('search')}:
                  </label>
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder={locale === 'ar' ? 'ابحث عن منتج...' : 'Search for a product...'}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('categories')}:
                  </label>
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="all">
                      {locale === 'ar' ? 'جميع الفئات' : 'All Categories'}
                    </option>
                    {categories.map((category) => (
                      <option key={category.id} value={category.id}>
                        {locale === 'ar' ? category.name_ar : category.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>

            {/* Products Grid */}
            {loading ? (
              <div className="text-center py-16">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
                <p className="text-gray-600">{t('loading')}</p>
              </div>
            ) : filteredProducts.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8">
                {filteredProducts.map((product) => (
                  <ProductCard
                    key={product.id}
                    id={product.id}
                    image={product.images?.[0]?.image_url || '/api/placeholder?width=400&height=300&text=لا توجد صورة'}
                    title={locale === 'ar' ? product.title_ar : product.title}
                    description={locale === 'ar' ? (product.description_ar || '') : (product.description || '')}
                    price={product.price}
                    available={product.is_available}
                    locale={locale}
                  />
                ))}
              </div>
            ) : (
              <div className="text-center py-16">
                <i className="ri-search-line text-6xl text-gray-400 mb-4"></i>
                <h3 className="text-xl font-semibold text-gray-800 mb-2">
                  {locale === 'ar' ? 'لا توجد منتجات' : 'No products found'}
                </h3>
                <p className="text-gray-600">
                  {locale === 'ar' 
                    ? 'جرب تغيير معايير البحث أو الفلترة'
                    : 'Try changing your search or filter criteria'
                  }
                </p>
              </div>
            )}
          </div>
        </section>
      </main>
      <Footer locale={locale} />
      <WhatsAppButton locale={locale} />
    </>
  );
}

// Loading component for Suspense fallback
function ProductsLoading() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-primary py-12">
        <div className="container mx-auto px-4">
          <div className="animate-pulse">
            <div className="h-8 bg-white/20 rounded w-48 mx-auto mb-4"></div>
            <div className="h-4 bg-white/10 rounded w-96 mx-auto"></div>
          </div>
        </div>
      </div>
      <div className="container mx-auto px-4 py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-600">جاري التحميل...</p>
        </div>
      </div>
    </div>
  );
}

// Main page component
export default function ProductsPage() {
  const params = useParams();
  const locale = (params?.locale || 'ar') as Locale;

  return (
    <>
      <Navbar locale={locale} />
      <Suspense fallback={<ProductsLoading />}>
        <ProductsContent locale={locale} />
      </Suspense>
    </>
  );
}
